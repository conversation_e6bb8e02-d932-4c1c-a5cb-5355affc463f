"use client";
import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import * as PopoverPrimitive from "@radix-ui/react-popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/index";

interface MonthPickerProps {
  value?: string;
  onChange: (val: string) => void;
  placeholder?: string;
}

const months = [
  "January", "February", "March", "April",
  "May", "June", "July", "August",
  "September", "October", "November", "December"
];

const monthsShort = [
  "Jan", "Feb", "Mar", "Apr",
  "May", "Jun", "Jul", "Aug",
  "Sep", "Oct", "Nov", "Dec"
];

export default function MonthPicker({
  value,
  onChange,
  placeholder = "Select Month"
}: MonthPickerProps) {
  const [open, setOpen] = React.useState(false);

  // Debug logging
  React.useEffect(() => {
    console.log("MonthPicker open state:", open);
  }, [open]);

  // Click outside handler
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (open && !target.closest('.month-picker-container')) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [open]);

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  // Initialize with current year but no selected month
  const [year, setYear] = React.useState<number>(currentYear);
  const [selectedMonth, setSelectedMonth] = React.useState<number | null>(null);

  // Update state when value prop changes
  React.useEffect(() => {
    if (value) {
      try {
        const date = new Date(value + "-01"); // Add day to make it a valid date
        if (!isNaN(date.getTime())) {
          setYear(date.getFullYear());
          setSelectedMonth(date.getMonth());
        }
      } catch (error) {
        console.error("Invalid date format:", value);
      }
    } else {
      setSelectedMonth(null);
      setYear(currentYear);
    }
  }, [value, currentYear]);

  const handleMonthSelect = (monthIndex: number) => {
    const selectedDate = new Date(year, monthIndex);
    setSelectedMonth(monthIndex);
    onChange(format(selectedDate, "yyyy-MM"));
    setOpen(false);
  };

  const handlePrevYear = () => {
    setYear((prevYear) => prevYear - 1);
  };

  const handleNextYear = () => {
    setYear((prevYear) => prevYear + 1);
  };

  const getDisplayText = () => {
    if (selectedMonth !== null) {
      return `${months[selectedMonth]} ${year}`;
    }
    return placeholder;
  };

  const isMonthDisabled = (monthIndex: number) => {
    // Only disable past months in the current year
    return year === currentYear && monthIndex < currentMonth;
  };

  const isMonthSelected = (monthIndex: number) => {
    return selectedMonth === monthIndex && value;
  };

  return (
    <div className="relative">
      <Button
        variant="outline"
        className={cn(
          "w-full h-8 text-sm justify-start text-left font-normal",
          !value && "text-muted-foreground"
        )}
        type="button"
        onClick={() => setOpen(!open)}
      >
        <CalendarIcon className="mr-2 h-4 w-4" />
        {getDisplayText()}
      </Button>

      {open && (
        <div
          className="absolute top-full left-0 mt-2 w-[280px] p-4 bg-white dark:bg-neutral-900 shadow-2xl border border-gray-200 dark:border-neutral-800 rounded-lg z-[9999]"
          style={{
            position: 'absolute',
            zIndex: 99999,
          }}
        >
          {/* Year navigation */}
          <div className="flex justify-between items-center mb-4">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handlePrevYear}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="font-semibold text-lg">{year}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleNextYear}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Month grid */}
          <div className="grid grid-cols-3 gap-2">
            {monthsShort.map((monthShort, index) => {
              const isDisabled = isMonthDisabled(index);
              const isSelected = isMonthSelected(index);

              return (
                <button
                  key={monthShort}
                  onClick={() => !isDisabled && handleMonthSelect(index)}
                  disabled={isDisabled}
                  className={cn(
                    "py-3 px-2 text-sm rounded-md transition-all duration-200 font-medium",
                    "hover:bg-gray-100 dark:hover:bg-neutral-800",
                    "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
                    isDisabled && "text-gray-400 cursor-not-allowed opacity-50 hover:bg-transparent",
                    isSelected && "bg-purple-500 text-white hover:bg-purple-600 shadow-md",
                    !isSelected && !isDisabled && "text-gray-700 dark:text-gray-200"
                  )}
                >
                  {monthShort}
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
